import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
    Dialog,
    DialogContent,
} from '@/components/ui/dialog';

import {
    ArrowLeft,
    Heart,
    Share2,
    Package,
    Smartphone,
    CheckCircle,
    AlertCircle,
    Table,
    List,
    Building,
    Hash,
    FileText,
    Settings,
    Image as ImageIcon,
    ChevronLeft,
    ChevronRight,
    X
} from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { AutoWatermark } from '@/components/Watermark';
import CompatibleModelsProtection from '@/components/security/CompatibleModelsProtection';
import { useState } from 'react';

interface PartSpecifications {
    [key: string]: string | number | boolean | null;
}

interface Part {
    id: number;
    name: string;
    slug?: string;
    part_number: string | null;
    manufacturer: string | null;
    description: string | null;
    specifications: PartSpecifications | null;
    images: string[] | null;
    category: {
        id: number;
        name: string;
        description: string | null;
    };
    models: Array<{
        id: number;
        name: string;
        model_number: string | null;
        release_year: number | null;
        brand: {
            id: number;
            name: string;
        };
        pivot: {
            compatibility_notes: string | null;
            is_verified: boolean;
        };
    }>;
}

interface Props {
    part: Part;
}

export default function PartDetails({ part }: Props) {
    const [isSaved, setIsSaved] = useState(false);
    const [isAddingToFavorites, setIsAddingToFavorites] = useState(false);
    const [viewMode, setViewMode] = useState<'list' | 'table'>('table'); // Default to table view
    const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
    const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false);

    const handleShare = () => {
        if (navigator.share) {
            navigator.share({
                title: part.name,
                text: `Check out this mobile part: ${part.name}`,
                url: window.location.href,
            });
        } else {
            navigator.clipboard.writeText(window.location.href);
            // You could show a toast notification here
        }
    };

    const handleAddToFavorites = () => {
        setIsAddingToFavorites(true);

        router.post(route('dashboard.add-favorite'), {
            type: 'part',
            id: part.id,
        }, {
            onSuccess: () => {
                setIsSaved(true);
                setIsAddingToFavorites(false);
                // Success message will be shown via backend flash message
            },
            onError: (errors) => {
                setIsAddingToFavorites(false);
                // Error messages will be shown via backend flash message
            }
        });
    };

    const handleImageClick = (index: number) => {
        setSelectedImageIndex(index);
        setIsImagePreviewOpen(true);
    };

    const handleCloseImagePreview = () => {
        setIsImagePreviewOpen(false);
        setSelectedImageIndex(null);
    };

    const handlePreviousImage = () => {
        if (selectedImageIndex !== null && part.images && selectedImageIndex > 0) {
            setSelectedImageIndex(selectedImageIndex - 1);
        }
    };

    const handleNextImage = () => {
        if (selectedImageIndex !== null && part.images && selectedImageIndex < part.images.length - 1) {
            setSelectedImageIndex(selectedImageIndex + 1);
        }
    };

    // Table View Component for Compatible Models
    const CompatibleModelsTableView = () => (
        <CompatibleModelsProtection className="relative">
            <div className="border border-gray-400 dark:border-gray-600 rounded-lg overflow-hidden bg-white dark:bg-gray-900">
                <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                        <thead className="bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/40 dark:to-indigo-900/40">
                            <tr>
                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100 border-r border-gray-400 dark:border-gray-600 first:rounded-tl-lg last:rounded-tr-lg">Brand</th>
                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100 border-r border-gray-400 dark:border-gray-600">Model</th>
                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100 border-r border-gray-400 dark:border-gray-600 hidden sm:table-cell">Model Number</th>
                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100 border-r border-gray-400 dark:border-gray-600 hidden lg:table-cell">Notes</th>
                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100 last:rounded-tr-lg">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {part.models.map((model, index) => (
                                <tr key={model.id} className={`border-b border-gray-400 dark:border-gray-600 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors ${
                                    index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50/50 dark:bg-gray-800/50'
                                } ${index === part.models.length - 1 ? 'last:border-b-0' : ''}`}>
                                    <td className="p-3 border-r border-gray-400 dark:border-gray-600">
                                        <div className="flex items-center gap-2">
                                            <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/50 rounded flex items-center justify-center flex-shrink-0">
                                                <Smartphone className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                            </div>
                                            <span className="font-semibold text-gray-900 dark:text-gray-100 text-sm">{model.brand.name}</span>
                                        </div>
                                    </td>
                                    <td className="p-3 border-r border-gray-400 dark:border-gray-600">
                                        <div>
                                            <p className="font-medium text-gray-800 dark:text-gray-200 text-sm">{model.name}</p>
                                            {model.model_number && (
                                                <p className="text-xs text-gray-500 dark:text-gray-400 font-mono sm:hidden">{model.model_number}</p>
                                            )}
                                            {model.pivot.compatibility_notes && (
                                                <p className="text-xs text-gray-500 dark:text-gray-400 lg:hidden truncate mt-1">{model.pivot.compatibility_notes}</p>
                                            )}
                                        </div>
                                    </td>
                                    <td className="p-3 border-r border-gray-400 dark:border-gray-600 hidden sm:table-cell">
                                        <span className="text-sm text-gray-600 dark:text-gray-300 font-mono">
                                            {model.model_number || '-'}
                                        </span>
                                    </td>
                                    <td className="p-3 border-r border-gray-400 dark:border-gray-600 hidden lg:table-cell">
                                        <span className="text-sm text-gray-600 dark:text-gray-300">
                                            {model.pivot.compatibility_notes || '-'}
                                        </span>
                                    </td>
                                    <td className="p-3">
                                        {model.pivot.is_verified ? (
                                            <Badge variant="default" className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">
                                                <CheckCircle className="w-3 h-3 mr-1" />
                                                Verified
                                            </Badge>
                                        ) : (
                                            <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800">
                                                <AlertCircle className="w-3 h-3 mr-1" />
                                                Unverified
                                            </Badge>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
            <AutoWatermark />
        </CompatibleModelsProtection>
    );

    // List View Component for Compatible Models
    const CompatibleModelsListView = () => (
        <CompatibleModelsProtection className="relative">
            <div className="space-y-3">
                {part.models.map((model) => (
                    <div key={model.id} className="flex items-center justify-between p-4 border border-blue-200/50 dark:border-blue-800/50 rounded-lg hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30">
                        <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center">
                                <Smartphone className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                                <div className="font-medium text-gray-900 dark:text-gray-100">
                                    {model.brand.name} {model.name}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-300">
                                    {model.model_number && `Model: ${model.model_number}`}
                                    {model.release_year && ` • ${model.release_year}`}
                                </div>
                                {model.pivot.compatibility_notes && (
                                    <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                        Note: {model.pivot.compatibility_notes}
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            {model.pivot.is_verified ? (
                                <Badge variant="default" className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">
                                    <CheckCircle className="w-3 h-3 mr-1" />
                                    Verified
                                </Badge>
                            ) : (
                                <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800">
                                    <AlertCircle className="w-3 h-3 mr-1" />
                                    Unverified
                                </Badge>
                            )}
                        </div>
                    </div>
                ))}
            </div>
            <AutoWatermark />
        </CompatibleModelsProtection>
    );

    return (
        <AppLayout>
            <Head title={part.name} />

            <div className="flex h-full flex-1 flex-col gap-3 rounded-xl p-3 sm:p-4 overflow-x-auto">
                <div className="space-y-3 sm:space-y-4">
                    {/* Header */}
                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-xl border border-blue-200/50 dark:border-blue-800/50 p-4 mb-4">
                        {/* Navigation */}
                        <div className="mb-3">
                            <Link href={route('search.index')}>
                                <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Search
                                </Button>
                            </Link>
                        </div>

                        {/* Main Header Content */}
                        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                            {/* Title Section */}
                            <div className="flex-1 min-w-0">
                                <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-2">
                                    <h1 className="text-xl sm:text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 truncate">
                                        {part.name}
                                    </h1>
                                    <div className="flex items-center gap-2 flex-shrink-0">
                                        {part.part_number && (
                                            <Badge variant="outline" className="font-mono text-xs">
                                                #{part.part_number}
                                            </Badge>
                                        )}
                                    </div>
                                </div>

                                {/* Compact Info Row */}
                                <div className="flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-muted-foreground mb-3">
                                    <div className="flex items-center gap-1">
                                        <Package className="w-3 h-3" />
                                        <span className="font-medium">{part.category.name}</span>
                                    </div>
                                    {part.manufacturer && (
                                        <div className="flex items-center gap-1">
                                            <Building className="w-3 h-3" />
                                            <span className="font-medium">{part.manufacturer}</span>
                                        </div>
                                    )}
                                    <div className="flex items-center gap-1">
                                        <Smartphone className="w-3 h-3" />
                                        <span className="font-medium">{part.models?.length || 0} models</span>
                                    </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex flex-col sm:flex-row gap-2 flex-shrink-0">
                                <Button
                                    size="sm"
                                    className="bg-green-600 hover:bg-green-700 text-white"
                                    onClick={handleAddToFavorites}
                                    disabled={isSaved || isAddingToFavorites}
                                >
                                    <Heart className={`w-4 h-4 mr-2 ${isSaved ? 'fill-current' : ''}`} />
                                    {isSaved ? 'Saved' : isAddingToFavorites ? 'Saving...' : 'Save'}
                                </Button>
                                <Button variant="outline" size="sm" onClick={handleShare}>
                                    <Share2 className="w-4 h-4 mr-2" />
                                    Share
                                </Button>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 lg:gap-4">
                        {/* Main Information */}
                        <div className="lg:col-span-3 space-y-3 lg:space-y-4">
                            {/* Basic Information and Specifications Side by Side */}
                            <div className="grid grid-cols-1 xl:grid-cols-2 gap-3 lg:gap-4">
                                {/* Basic Information */}
                                <Card className="border-blue-200/50 dark:border-blue-800/50">
                                    <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3">
                                        <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                            <Package className="w-4 h-4" />
                                            Basic Information
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="p-0">
                                        {/* Basic Info Table */}
                                        <div className="border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50 mx-4 mb-4">
                                            <div className="overflow-x-auto">
                                                <table className="w-full">
                                                    <tbody>
                                                        <tr className="border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30">
                                                            <td className="p-3 w-1/3">
                                                                <span className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                                                    <Package className="w-3 h-3 text-blue-600 dark:text-blue-400" />
                                                                    Category
                                                                </span>
                                                            </td>
                                                            <td className="p-3">
                                                                <span className="text-gray-800 dark:text-gray-200 font-medium">
                                                                    {part.category.name}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                        {part.part_number && (
                                                            <tr className="border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-blue-50/30 dark:bg-blue-950/10">
                                                                <td className="p-3">
                                                                    <span className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                                                        <Hash className="w-3 h-3 text-green-600 dark:text-green-400" />
                                                                        Part Number
                                                                    </span>
                                                                </td>
                                                                <td className="p-3">
                                                                    <span className="text-gray-800 dark:text-gray-200 font-medium font-mono">
                                                                        {part.part_number}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        )}
                                                        {part.manufacturer && (
                                                            <tr className="hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors bg-white/60 dark:bg-gray-800/30">
                                                                <td className="p-3">
                                                                    <span className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                                                        <Building className="w-3 h-3 text-purple-600 dark:text-purple-400" />
                                                                        Manufacturer
                                                                    </span>
                                                                </td>
                                                                <td className="p-3">
                                                                    <span className="text-gray-800 dark:text-gray-200 font-medium">
                                                                        {part.manufacturer}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        )}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        {/* Description */}
                                        {part.description && (
                                            <div className="mx-4 mb-4 p-3 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-900/50 dark:to-slate-900/50 rounded-lg border border-gray-200 dark:border-gray-700">
                                                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-1 mb-2">
                                                    <FileText className="w-3 h-3" />
                                                    Description
                                                </Label>
                                                <p className="text-sm leading-relaxed whitespace-pre-wrap text-gray-800 dark:text-gray-200">{part.description}</p>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>

                                {/* Specifications */}
                                {part.specifications && Object.keys(part.specifications).length > 0 && (
                                    <Card className="border-blue-200/50 dark:border-blue-800/50">
                                        <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3">
                                            <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                                <Settings className="w-4 h-4" />
                                                Specifications
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="p-0">
                                            <div className="border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden bg-white/50 dark:bg-gray-900/50 mx-4 mb-4">
                                                <div className="overflow-x-auto">
                                                    <table className="w-full">
                                                        <thead className="bg-gradient-to-r from-blue-100/80 to-indigo-100/80 dark:from-blue-900/40 dark:to-indigo-900/40">
                                                            <tr>
                                                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100 w-1/2">Specification</th>
                                                                <th className="text-left p-3 font-semibold text-blue-900 dark:text-blue-100">Value</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {Object.entries(part.specifications).map(([key, value], index) => (
                                                                <tr key={key} className={`border-b border-blue-200/30 dark:border-blue-800/30 hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-colors ${
                                                                    index % 2 === 0 ? 'bg-white/60 dark:bg-gray-800/30' : 'bg-blue-50/30 dark:bg-blue-950/10'
                                                                }`}>
                                                                    <td className="p-3">
                                                                        <span className="font-semibold text-gray-900 dark:text-gray-100 capitalize">
                                                                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                                                                        </span>
                                                                    </td>
                                                                    <td className="p-3">
                                                                        <span className="text-gray-800 dark:text-gray-200 font-medium">
                                                                            {value}
                                                                        </span>
                                                                    </td>
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                )}
                            </div>

                            {/* Compatible Models */}
                            {part.models && part.models.length > 0 && (
                                <Card className="border-blue-200/50 dark:border-blue-800/50">
                                    <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3">
                                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                                            <div className="flex-1 min-w-0">
                                                <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                                    <Smartphone className="w-4 h-4" />
                                                    Compatible Models ({part.models.length})
                                                </CardTitle>
                                                <CardDescription className="mt-1 text-blue-700/70 dark:text-blue-300/70 text-sm">
                                                    Mobile device models that are compatible with this part
                                                </CardDescription>
                                            </div>
                                            {/* View Mode Toggle */}
                                            <div className="flex items-center gap-1 bg-white/60 dark:bg-gray-800/60 border border-blue-200 dark:border-blue-700 rounded-lg p-1">
                                                <Button
                                                    variant={viewMode === 'table' ? 'default' : 'ghost'}
                                                    size="sm"
                                                    onClick={() => setViewMode('table')}
                                                    className={`h-7 px-2 ${viewMode === 'table'
                                                        ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm'
                                                        : 'hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                                                    }`}
                                                    title="Table View"
                                                >
                                                    <Table className="h-3 w-3" />
                                                    <span className="ml-1 hidden sm:inline text-xs">Table</span>
                                                </Button>
                                                <Button
                                                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                                                    size="sm"
                                                    onClick={() => setViewMode('list')}
                                                    className={`h-7 px-2 ${viewMode === 'list'
                                                        ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm'
                                                        : 'hover:bg-blue-100 dark:hover:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                                                    }`}
                                                    title="List View"
                                                >
                                                    <List className="h-3 w-3" />
                                                    <span className="ml-1 hidden sm:inline text-xs">List</span>
                                                </Button>
                                            </div>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="p-4">
                                        {/* Conditional rendering based on view mode */}
                                        {viewMode === 'table' ? (
                                            <CompatibleModelsTableView />
                                        ) : (
                                            <CompatibleModelsListView />
                                        )}
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-4">
                            {/* Images Section */}
                            {part.images && part.images.length > 0 && (
                                <Card className="border-blue-200/50 dark:border-blue-800/50">
                                    <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3">
                                        <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                            <ImageIcon className="w-4 h-4" />
                                            Images ({part.images.length})
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="p-3">
                                        <div className="grid grid-cols-3 gap-2">
                                            {part.images.map((image, index) => (
                                                <div
                                                    key={index}
                                                    className="aspect-square w-full max-w-[80px] mx-auto border border-blue-200/50 dark:border-blue-800/50 rounded-lg overflow-hidden group cursor-pointer hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300"
                                                    onClick={() => handleImageClick(index)}
                                                >
                                                    <img
                                                        src={image}
                                                        alt={`${part.name} - Image ${index + 1}`}
                                                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                                        onError={(e) => {
                                                            e.currentTarget.src = '/placeholder-image.svg';
                                                        }}
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Combined Quick Info and Actions */}
                            <Card className="border-blue-200/50 dark:border-blue-800/50">
                                {/* Quick Info Section */}
                                <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3">
                                    <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                        <Package className="w-4 h-4" />
                                        Quick Info
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="p-3 pb-0">
                                    <div className="space-y-3">
                                        <div className="bg-white/60 dark:bg-gray-800/60 rounded-lg p-3 border border-blue-200/50 dark:border-blue-700">
                                            <div className="flex items-center gap-2 mb-1">
                                                <Package className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Category</span>
                                            </div>
                                            <p className="font-semibold text-gray-900 dark:text-gray-100">{part.category.name}</p>
                                            {part.category.description && (
                                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{part.category.description}</p>
                                            )}
                                        </div>

                                        <div className="bg-white/60 dark:bg-gray-800/60 rounded-lg p-3 border border-blue-200/50 dark:border-blue-700">
                                            <div className="flex items-center gap-2 mb-1">
                                                <Smartphone className="w-4 h-4 text-green-600 dark:text-green-400" />
                                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Compatibility</span>
                                            </div>
                                            <p className="font-semibold text-gray-900 dark:text-gray-100">
                                                {part.models.length} {part.models.length === 1 ? 'Model' : 'Models'}
                                            </p>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">Compatible devices</p>
                                        </div>
                                    </div>
                                </CardContent>

                                {/* Actions Section */}
                                <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/10 dark:to-indigo-950/10 pb-3 border-t border-blue-200/50 dark:border-blue-800/50 mt-3">
                                    <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100 text-lg">
                                        <Heart className="w-4 h-4" />
                                        Actions
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2 p-3">
                                    <div className="space-y-2">
                                        <Button
                                            className="w-full bg-green-600 hover:bg-green-700 text-white border-0 justify-start text-sm"
                                            onClick={handleAddToFavorites}
                                            disabled={isSaved || isAddingToFavorites}
                                        >
                                            <Heart className={`w-3 h-3 mr-2 ${isSaved ? 'fill-current' : ''}`} />
                                            {isSaved ? 'Added to Favorites' : isAddingToFavorites ? 'Adding...' : 'Add to Favorites'}
                                        </Button>
                                        <Button
                                            className="w-full bg-blue-600 hover:bg-blue-700 text-white border-0 justify-start text-sm"
                                            onClick={handleShare}
                                        >
                                            <Share2 className="w-3 h-3 mr-2" />
                                            Share Part
                                        </Button>
                                        <Link href={route('search.index')}>
                                            <Button className="w-full justify-start text-sm" variant="outline">
                                                <ArrowLeft className="w-3 h-3 mr-2" />
                                                Back to Search
                                            </Button>
                                        </Link>
                                    </div>
                                </CardContent>
                            </Card>


                        </div>
                    </div>
                </div>
            </div>

            {/* Image Preview Modal */}
            <Dialog open={isImagePreviewOpen} onOpenChange={handleCloseImagePreview}>
                <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
                    <div className="relative bg-black">
                        {/* Close Button */}
                        <Button
                            variant="ghost"
                            size="sm"
                            className="absolute top-2 right-2 z-10 text-white hover:bg-white/20 rounded-full w-8 h-8 p-0"
                            onClick={handleCloseImagePreview}
                        >
                            <X className="w-4 h-4" />
                        </Button>

                        {/* Navigation Buttons */}
                        {part.images && part.images.length > 1 && (
                            <>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="absolute left-2 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-full w-10 h-10 p-0 disabled:opacity-50"
                                    onClick={handlePreviousImage}
                                    disabled={selectedImageIndex === 0}
                                >
                                    <ChevronLeft className="w-6 h-6" />
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="absolute right-2 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-full w-10 h-10 p-0 disabled:opacity-50"
                                    onClick={handleNextImage}
                                    disabled={selectedImageIndex === (part.images?.length || 0) - 1}
                                >
                                    <ChevronRight className="w-6 h-6" />
                                </Button>
                            </>
                        )}

                        {/* Image */}
                        {selectedImageIndex !== null && part.images && (
                            <div className="flex items-center justify-center min-h-[60vh] max-h-[80vh]">
                                <img
                                    src={part.images[selectedImageIndex]}
                                    alt={`${part.name} - Image ${selectedImageIndex + 1}`}
                                    className="max-w-full max-h-full object-contain"
                                    onError={(e) => {
                                        e.currentTarget.src = '/placeholder-image.svg';
                                    }}
                                />
                            </div>
                        )}

                        {/* Image Counter */}
                        {part.images && part.images.length > 1 && selectedImageIndex !== null && (
                            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                                {selectedImageIndex + 1} / {part.images.length}
                            </div>
                        )}
                    </div>
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
